/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["circle", { cx: "12", cy: "16", r: "1", key: "1au0dj" }],
  ["rect", { x: "3", y: "10", width: "18", height: "12", rx: "2", key: "6s8ecr" }],
  ["path", { d: "M7 10V7a5 5 0 0 1 10 0v3", key: "1pqi11" }]
];
const LockKeyhole = createLucideIcon("lock-keyhole", __iconNode);

export { __iconNode, LockKeyhole as default };//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/bfcd46e04becbf1670511e8c1e9cb0f1c1d62983/node_modules/lucide-react/dist/esm/icons/lock-keyhole.js.map
