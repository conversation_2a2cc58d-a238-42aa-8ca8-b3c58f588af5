#!/usr/bin/env python3
"""
Server startup script with verbose output
"""
import os
import sys

def main():
    print("🚀 Starting Watson Sentiment Analysis Server")
    print("=" * 50)
    
    try:
        print("1. Setting up environment...")
        os.environ['FLASK_DEBUG'] = '1'
        
        print("2. Importing Flask app...")
        from app import create_app
        print("   ✅ App module imported successfully")
        
        print("3. Creating Flask application...")
        app = create_app()
        print("   ✅ Flask app created successfully")
        
        print("4. Starting development server...")
        print("   🌐 Server will be available at: http://localhost:5000")
        print("   📝 Press Ctrl+C to stop the server")
        print("-" * 50)
        
        # Start the server
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=True,
            use_reloader=False  # Disable reloader to avoid issues
        )
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Make sure all dependencies are installed:")
        print("   pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
