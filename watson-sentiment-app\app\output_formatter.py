"""
Output Formatting Module for Watson Sentiment Analysis

This module provides functions to format sentiment analysis results in a user-friendly way
for both console and web display.
"""

import json
from typing import Dict, Any, List, Optional
from datetime import datetime


class SentimentFormatter:
    """
    Formats sentiment analysis results for different output formats
    """

    @staticmethod
    def format_sentiment_result(raw_result: Dict[str, Any], input_text: str = None) -> Dict[str, Any]:
        """
        Format raw Watson NLU sentiment result into a structured format

        Args:
            raw_result (Dict[str, Any]): Raw result from Watson NLU API
            input_text (str, optional): Original input text

        Returns:
            Dict[str, Any]: Formatted sentiment analysis result
        """
        try:
            # Extract sentiment information
            sentiment_data = raw_result.get('sentiment', {})
            document_sentiment = sentiment_data.get('document', {})

            # Get basic sentiment info
            label = document_sentiment.get('label', 'neutral')
            score = document_sentiment.get('score', 0.0)

            # Calculate confidence percentage
            confidence = abs(score) * 100

            # Determine sentiment classification
            if label.lower() == 'positive':
                classification = 'Positive'
                emoji = '😊'
                color_class = 'positive'
            elif label.lower() == 'negative':
                classification = 'Negative'
                emoji = '😞'
                color_class = 'negative'
            else:
                classification = 'Neutral'
                emoji = '😐'
                color_class = 'neutral'

            # Format the result
            formatted_result = {
                'sentiment': {
                    'classification': classification,
                    'label': label,
                    'score': round(score, 4),
                    'confidence': round(confidence, 2),
                    'emoji': emoji,
                    'color_class': color_class
                },
                'analysis': {
                    'text_length': len(input_text) if input_text else 0,
                    'analyzed_at': datetime.now().isoformat(),
                    'language': raw_result.get('language', 'en')
                },
                'raw_data': raw_result
            }

            # Add text preview if provided
            if input_text:
                preview_length = 100
                formatted_result['text_preview'] = (
                    input_text[:preview_length] + '...'
                    if len(input_text) > preview_length
                    else input_text
                )

            return formatted_result

        except Exception as e:
            return {
                'error': f"Failed to format sentiment result: {str(e)}",
                'raw_data': raw_result
            }

    @staticmethod
    def format_for_console(formatted_result: Dict[str, Any]) -> str:
        """
        Format sentiment result for console display

        Args:
            formatted_result (Dict[str, Any]): Formatted sentiment result

        Returns:
            str: Console-formatted output
        """
        if 'error' in formatted_result:
            return f"❌ Error: {formatted_result['error']}"

        sentiment = formatted_result.get('sentiment', {})
        analysis = formatted_result.get('analysis', {})

        output = []
        output.append("=" * 50)
        output.append("📊 SENTIMENT ANALYSIS RESULTS")
        output.append("=" * 50)

        # Text preview
        if 'text_preview' in formatted_result:
            output.append(f"📝 Text: {formatted_result['text_preview']}")
            output.append("")

        # Sentiment information
        output.append(f"{sentiment.get('emoji', '')} Sentiment: {sentiment.get('classification', 'Unknown')}")
        output.append(f"📈 Confidence: {sentiment.get('confidence', 0)}%")
        output.append(f"🎯 Score: {sentiment.get('score', 0)}")
        output.append("")

        # Analysis metadata
        output.append(f"📏 Text Length: {analysis.get('text_length', 0)} characters")
        output.append(f"🌐 Language: {analysis.get('language', 'Unknown')}")
        output.append(f"⏰ Analyzed: {analysis.get('analyzed_at', 'Unknown')}")
        output.append("=" * 50)

        return "\n".join(output)

    @staticmethod
    def format_for_web(formatted_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Format sentiment result for web display

        Args:
            formatted_result (Dict[str, Any]): Formatted sentiment result

        Returns:
            Dict[str, Any]: Web-formatted output
        """
        if 'error' in formatted_result:
            return {
                'success': False,
                'error': formatted_result['error'],
                'display_data': {
                    'title': 'Analysis Error',
                    'message': formatted_result['error'],
                    'type': 'error'
                }
            }

        sentiment = formatted_result.get('sentiment', {})
        analysis = formatted_result.get('analysis', {})

        # Create web-friendly display data
        display_data = {
            'title': 'Sentiment Analysis Results',
            'sentiment': {
                'classification': sentiment.get('classification', 'Unknown'),
                'emoji': sentiment.get('emoji', ''),
                'confidence': sentiment.get('confidence', 0),
                'score': sentiment.get('score', 0),
                'color_class': sentiment.get('color_class', 'neutral'),
                'confidence_bar_width': min(sentiment.get('confidence', 0), 100)
            },
            'metadata': {
                'text_length': analysis.get('text_length', 0),
                'language': analysis.get('language', 'Unknown'),
                'analyzed_at': analysis.get('analyzed_at', 'Unknown')
            },
            'text_preview': formatted_result.get('text_preview', ''),
            'type': 'success'
        }

        return {
            'success': True,
            'display_data': display_data,
            'raw_result': formatted_result
        }

    @staticmethod
    def format_batch_results(batch_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Format batch sentiment analysis results

        Args:
            batch_results (Dict[str, Any]): Batch analysis results

        Returns:
            Dict[str, Any]: Formatted batch results
        """
        formatted_results = []

        # Format successful results
        for result in batch_results.get('results', []):
            formatted_result = SentimentFormatter.format_sentiment_result(
                result['result'],
                result['text']
            )
            formatted_result['index'] = result['index']
            formatted_results.append(formatted_result)

        # Calculate summary statistics
        if formatted_results:
            positive_count = sum(1 for r in formatted_results
                               if r.get('sentiment', {}).get('classification') == 'Positive')
            negative_count = sum(1 for r in formatted_results
                               if r.get('sentiment', {}).get('classification') == 'Negative')
            neutral_count = sum(1 for r in formatted_results
                              if r.get('sentiment', {}).get('classification') == 'Neutral')

            avg_confidence = sum(r.get('sentiment', {}).get('confidence', 0)
                               for r in formatted_results) / len(formatted_results)
        else:
            positive_count = negative_count = neutral_count = 0
            avg_confidence = 0

        return {
            'summary': {
                'total_analyzed': batch_results.get('successful_analyses', 0),
                'total_failed': batch_results.get('failed_analyses', 0),
                'positive_count': positive_count,
                'negative_count': negative_count,
                'neutral_count': neutral_count,
                'average_confidence': round(avg_confidence, 2)
            },
            'results': formatted_results,
            'errors': batch_results.get('errors', [])
        }

    @staticmethod
    def format_for_json(formatted_result: Dict[str, Any], pretty: bool = True) -> str:
        """
        Format sentiment result as JSON string

        Args:
            formatted_result (Dict[str, Any]): Formatted sentiment result
            pretty (bool): Whether to format JSON with indentation

        Returns:
            str: JSON-formatted output
        """
        try:
            if pretty:
                return json.dumps(formatted_result, indent=2, ensure_ascii=False)
            else:
                return json.dumps(formatted_result, ensure_ascii=False)
        except Exception as e:
            return json.dumps({'error': f'Failed to format JSON: {str(e)}'}, indent=2)

    @staticmethod
    def get_sentiment_summary(formatted_result: Dict[str, Any]) -> str:
        """
        Get a brief summary of the sentiment analysis

        Args:
            formatted_result (Dict[str, Any]): Formatted sentiment result

        Returns:
            str: Brief sentiment summary
        """
        if 'error' in formatted_result:
            return "Analysis failed"

        sentiment = formatted_result.get('sentiment', {})
        classification = sentiment.get('classification', 'Unknown')
        confidence = sentiment.get('confidence', 0)
        emoji = sentiment.get('emoji', '')

        return f"{emoji} {classification} ({confidence:.1f}% confidence)"


# Convenience functions for direct use
def format_sentiment_result(raw_result: Dict[str, Any], input_text: str = None) -> Dict[str, Any]:
    """Convenience function for formatting sentiment results"""
    return SentimentFormatter.format_sentiment_result(raw_result, input_text)


def format_for_console(formatted_result: Dict[str, Any]) -> str:
    """Convenience function for console formatting"""
    return SentimentFormatter.format_for_console(formatted_result)


def format_for_web(formatted_result: Dict[str, Any]) -> Dict[str, Any]:
    """Convenience function for web formatting"""
    return SentimentFormatter.format_for_web(formatted_result)
