["tests/test_output_formatter.py::TestConvenienceFunctions::test_format_for_console_function", "tests/test_output_formatter.py::TestConvenienceFunctions::test_format_for_web_function", "tests/test_output_formatter.py::TestConvenienceFunctions::test_format_sentiment_result_function", "tests/test_output_formatter.py::TestSentimentFormatter::test_format_batch_results_success", "tests/test_output_formatter.py::TestSentimentFormatter::test_format_batch_results_with_errors", "tests/test_output_formatter.py::TestSentimentFormatter::test_format_for_console_error", "tests/test_output_formatter.py::TestSentimentFormatter::test_format_for_console_success", "tests/test_output_formatter.py::TestSentimentFormatter::test_format_for_json_compact", "tests/test_output_formatter.py::TestSentimentFormatter::test_format_for_json_error", "tests/test_output_formatter.py::TestSentimentFormatter::test_format_for_json_pretty", "tests/test_output_formatter.py::TestSentimentFormatter::test_format_for_web_error", "tests/test_output_formatter.py::TestSentimentFormatter::test_format_for_web_success", "tests/test_output_formatter.py::TestSentimentFormatter::test_format_sentiment_result_long_text", "tests/test_output_formatter.py::TestSentimentFormatter::test_format_sentiment_result_malformed_response", "tests/test_output_formatter.py::TestSentimentFormatter::test_format_sentiment_result_negative", "tests/test_output_formatter.py::TestSentimentFormatter::test_format_sentiment_result_neutral", "tests/test_output_formatter.py::TestSentimentFormatter::test_format_sentiment_result_no_input_text", "tests/test_output_formatter.py::TestSentimentFormatter::test_format_sentiment_result_positive", "tests/test_output_formatter.py::TestSentimentFormatter::test_get_sentiment_summary_error", "tests/test_output_formatter.py::TestSentimentFormatter::test_get_sentiment_summary_positive", "tests/test_routes.py::TestFlaskRoutes::test_404_error_handler", "tests/test_routes.py::TestFlaskRoutes::test_500_error_handler", "tests/test_routes.py::TestFlaskRoutes::test_about_route", "tests/test_routes.py::TestFlaskRoutes::test_analyze_batch_route_empty_list", "tests/test_routes.py::TestFlaskRoutes::test_analyze_batch_route_invalid_format", "tests/test_routes.py::TestFlaskRoutes::test_analyze_batch_route_no_valid_texts", "tests/test_routes.py::TestFlaskRoutes::test_analyze_batch_route_success", "tests/test_routes.py::TestFlaskRoutes::test_analyze_batch_route_too_many_texts", "tests/test_routes.py::TestFlaskRoutes::test_analyze_route_empty_text", "tests/test_routes.py::TestFlaskRoutes::test_analyze_route_form_data", "tests/test_routes.py::TestFlaskRoutes::test_analyze_route_missing_text", "tests/test_routes.py::TestFlaskRoutes::test_analyze_route_success", "tests/test_routes.py::TestFlaskRoutes::test_analyze_route_text_too_long", "tests/test_routes.py::TestFlaskRoutes::test_analyze_route_unexpected_error", "tests/test_routes.py::TestFlaskRoutes::test_analyze_route_watson_api_error", "tests/test_routes.py::TestFlaskRoutes::test_health_check_route_healthy", "tests/test_routes.py::TestFlaskRoutes::test_health_check_route_unhealthy", "tests/test_routes.py::TestFlaskRoutes::test_index_route", "tests/test_routes.py::TestGetSentimentAnalyzer::test_get_sentiment_analyzer_missing_credentials", "tests/test_routes.py::TestGetSentimentAnalyzer::test_get_sentiment_analyzer_singleton", "tests/test_routes.py::TestGetSentimentAnalyzer::test_get_sentiment_analyzer_success", "tests/test_sentiment_analyzer.py::TestCreateSentimentAnalyzer::test_create_sentiment_analyzer", "tests/test_sentiment_analyzer.py::TestSentimentAnalyzer::test_analyze_sentiment_api_exception", "tests/test_sentiment_analyzer.py::TestSentimentAnalyzer::test_analyze_sentiment_batch_empty_list", "tests/test_sentiment_analyzer.py::TestSentimentAnalyzer::test_analyze_sentiment_batch_success", "tests/test_sentiment_analyzer.py::TestSentimentAnalyzer::test_analyze_sentiment_batch_with_errors", "tests/test_sentiment_analyzer.py::TestSentimentAnalyzer::test_analyze_sentiment_empty_text", "tests/test_sentiment_analyzer.py::TestSentimentAnalyzer::test_analyze_sentiment_none_text", "tests/test_sentiment_analyzer.py::TestSentimentAnalyzer::test_analyze_sentiment_success", "tests/test_sentiment_analyzer.py::TestSentimentAnalyzer::test_analyze_sentiment_whitespace_only", "tests/test_sentiment_analyzer.py::TestSentimentAnalyzer::test_get_service_status_healthy", "tests/test_sentiment_analyzer.py::TestSentimentAnalyzer::test_get_service_status_unhealthy", "tests/test_sentiment_analyzer.py::TestSentimentAnalyzer::test_initialization_missing_api_key", "tests/test_sentiment_analyzer.py::TestSentimentAnalyzer::test_initialization_missing_service_url", "tests/test_sentiment_analyzer.py::TestSentimentAnalyzer::test_initialization_success", "tests/test_sentiment_analyzer.py::TestSentimentAnalyzerIntegration::test_real_watson_api_call"]