#!/usr/bin/env python3
"""
Demo script for Watson Sentiment Analysis Application

This script demonstrates the core functionality without requiring <PERSON> credentials.
"""

import sys
import os
from app.sentiment_analyzer import SentimentAnalyzer
from app.output_formatter import SentimentFormatter
from unittest.mock import Mock


def create_mock_watson_response(text, sentiment='positive'):
    """Create a mock Watson NLU response for demonstration"""
    scores = {
        'positive': 0.8,
        'negative': -0.6,
        'neutral': 0.1
    }
    
    return {
        'usage': {'text_units': 1, 'text_characters': len(text), 'features': 1},
        'sentiment': {
            'document': {
                'score': scores.get(sentiment, 0.0),
                'label': sentiment
            }
        },
        'language': 'en'
    }


def demo_sentiment_analysis():
    """Demonstrate sentiment analysis functionality"""
    print("🧪 Watson Sentiment Analysis Demo")
    print("=" * 50)
    
    # Sample texts for demonstration
    sample_texts = [
        ("I absolutely love this new smartphone! The camera quality is amazing.", "positive"),
        ("This product is terrible. It broke after one day.", "negative"),
        ("The weather today is partly cloudy with 72 degrees.", "neutral")
    ]
    
    for text, expected_sentiment in sample_texts:
        print(f"\n📝 Text: {text}")
        print(f"🎯 Expected Sentiment: {expected_sentiment.title()}")
        
        # Create mock response
        mock_response = create_mock_watson_response(text, expected_sentiment)
        
        # Format the result
        formatted_result = SentimentFormatter.format_sentiment_result(mock_response, text)
        
        # Display console output
        console_output = SentimentFormatter.format_for_console(formatted_result)
        print(console_output)
        
        # Display summary
        summary = SentimentFormatter.get_sentiment_summary(formatted_result)
        print(f"📊 Summary: {summary}")
        print("-" * 50)


def demo_batch_analysis():
    """Demonstrate batch sentiment analysis"""
    print("\n🔄 Batch Analysis Demo")
    print("=" * 30)
    
    texts = [
        "Great product, highly recommend!",
        "Worst purchase ever, complete waste of money.",
        "It's okay, nothing special but works fine.",
        "Amazing customer service and fast delivery!",
        "The item arrived damaged and unusable."
    ]
    
    # Create mock batch results
    results = []
    for i, text in enumerate(texts):
        if "great" in text.lower() or "amazing" in text.lower() or "recommend" in text.lower():
            sentiment = "positive"
        elif "worst" in text.lower() or "waste" in text.lower() or "damaged" in text.lower():
            sentiment = "negative"
        else:
            sentiment = "neutral"
        
        mock_response = create_mock_watson_response(text, sentiment)
        results.append({
            'index': i,
            'text': text,
            'result': mock_response
        })
    
    batch_results = {
        'successful_analyses': len(results),
        'failed_analyses': 0,
        'results': results,
        'errors': []
    }
    
    # Format batch results
    formatted_batch = SentimentFormatter.format_batch_results(batch_results)
    
    print(f"📊 Batch Analysis Summary:")
    print(f"   Total Analyzed: {formatted_batch['summary']['total_analyzed']}")
    print(f"   Positive: {formatted_batch['summary']['positive_count']}")
    print(f"   Negative: {formatted_batch['summary']['negative_count']}")
    print(f"   Neutral: {formatted_batch['summary']['neutral_count']}")
    print(f"   Average Confidence: {formatted_batch['summary']['average_confidence']}%")
    
    print(f"\n📝 Individual Results:")
    for result in formatted_batch['results']:
        sentiment = result['sentiment']
        print(f"   {sentiment['emoji']} \"{result['text_preview']}\" - {sentiment['classification']} ({sentiment['confidence']}%)")


def demo_output_formats():
    """Demonstrate different output formats"""
    print("\n🎨 Output Format Demo")
    print("=" * 25)
    
    text = "This is an excellent product that exceeded my expectations!"
    mock_response = create_mock_watson_response(text, "positive")
    formatted_result = SentimentFormatter.format_sentiment_result(mock_response, text)
    
    print("1. Console Format:")
    print(SentimentFormatter.format_for_console(formatted_result))
    
    print("\n2. Web Format:")
    web_result = SentimentFormatter.format_for_web(formatted_result)
    print(f"   Success: {web_result['success']}")
    print(f"   Title: {web_result['display_data']['title']}")
    print(f"   Sentiment: {web_result['display_data']['sentiment']['classification']}")
    print(f"   Confidence: {web_result['display_data']['sentiment']['confidence']}%")
    
    print("\n3. JSON Format:")
    json_output = SentimentFormatter.format_for_json(formatted_result, pretty=True)
    print(json_output[:200] + "..." if len(json_output) > 200 else json_output)


def demo_error_handling():
    """Demonstrate error handling"""
    print("\n⚠️  Error Handling Demo")
    print("=" * 25)
    
    # Test malformed response handling
    malformed_response = {'invalid': 'response'}
    result = SentimentFormatter.format_sentiment_result(malformed_response)
    
    print("Malformed Response Handling:")
    print(f"   Classification: {result['sentiment']['classification']}")
    print(f"   Confidence: {result['sentiment']['confidence']}%")
    print("   ✅ Gracefully handled malformed response")
    
    # Test error formatting
    error_result = {'error': 'Watson API temporarily unavailable'}
    console_error = SentimentFormatter.format_for_console(error_result)
    print(f"\nError Console Output:")
    print(console_error)


def main():
    """Main demo function"""
    print("🚀 Starting Watson Sentiment Analysis Demo")
    print("This demo shows the application functionality without requiring Watson credentials.\n")
    
    try:
        demo_sentiment_analysis()
        demo_batch_analysis()
        demo_output_formats()
        demo_error_handling()
        
        print("\n✅ Demo completed successfully!")
        print("\n📋 Next Steps:")
        print("1. Set up Watson NLP credentials in .env file")
        print("2. Run 'python run.py' to start the web application")
        print("3. Visit http://localhost:5000 to use the web interface")
        print("4. Run 'python -m pytest tests/' to execute the test suite")
        
    except Exception as e:
        print(f"❌ Demo failed with error: {e}")
        return 1
    
    return 0


if __name__ == '__main__':
    sys.exit(main())
