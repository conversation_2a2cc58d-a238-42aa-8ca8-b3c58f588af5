# Error Handling Guide

This document outlines the comprehensive error handling implemented in the Watson Sentiment Analysis application.

## Error Handling Strategy

The application implements a multi-layered error handling approach:

1. **Input Validation**: Validate user input before processing
2. **API Error Handling**: Handle Watson API failures gracefully
3. **Network Error Handling**: Manage network connectivity issues
4. **Graceful Degradation**: Provide meaningful feedback when services are unavailable
5. **Logging**: Comprehensive logging for debugging and monitoring

## Error Categories

### 1. Input Validation Errors

**Location**: `app/routes.py`, `app/sentiment_analyzer.py`

**Handled Errors**:
- Empty or None text input
- Text exceeding maximum length (10,000 characters)
- Invalid JSON format in API requests
- Missing required fields

**Example**:
```python
if not text or not text.strip():
    raise ValueError("Text input cannot be empty")

if len(text) > 10000:
    return jsonify({
        'success': False,
        'error': 'Text too long',
        'message': 'Text must be less than 10,000 characters'
    }), 400
```

### 2. Watson API Errors

**Location**: `app/sentiment_analyzer.py`, `app/routes.py`

**Handled Errors**:
- Authentication failures (invalid API key)
- Service unavailable (503 errors)
- Rate limiting (429 errors)
- Invalid requests (400 errors)
- Network timeouts

**Example**:
```python
try:
    response = self._nlu_service.analyze(text=text, features=features)
    return response.get_result()
except ApiException as e:
    logger.error(f"Watson API error: {str(e)}")
    raise
except Exception as e:
    logger.error(f"Unexpected error: {str(e)}")
    raise
```

### 3. Configuration Errors

**Location**: `app/routes.py`, `config.py`

**Handled Errors**:
- Missing Watson API credentials
- Invalid service URLs
- Configuration file issues

**Example**:
```python
if not api_key or not service_url:
    raise ValueError("Watson API credentials not configured")
```

### 4. Application Errors

**Location**: `app/routes.py`

**Handled Errors**:
- Template not found
- Internal server errors
- Database connection issues (if applicable)

## Error Response Formats

### JSON API Responses

All API endpoints return consistent error responses:

```json
{
    "success": false,
    "error": "error_type",
    "message": "Human-readable error message",
    "details": "Additional technical details (optional)"
}
```

### HTTP Status Codes

- `400 Bad Request`: Invalid input or malformed requests
- `401 Unauthorized`: Authentication failures
- `403 Forbidden`: Access denied
- `404 Not Found`: Resource not found
- `429 Too Many Requests`: Rate limiting
- `500 Internal Server Error`: Unexpected server errors
- `503 Service Unavailable`: Watson API unavailable

## Error Handling Implementation

### 1. Sentiment Analyzer Error Handling

```python
class SentimentAnalyzer:
    def analyze_sentiment(self, text: str) -> Dict[str, Any]:
        try:
            # Input validation
            if not text or not text.strip():
                raise ValueError("Text input cannot be empty")
            
            # API call
            response = self._nlu_service.analyze(...)
            return response.get_result()
            
        except ValueError as e:
            logger.error(f"Input validation error: {str(e)}")
            raise
        except ApiException as e:
            logger.error(f"Watson API error: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error: {str(e)}")
            raise
```

### 2. Flask Route Error Handling

```python
@main.route('/analyze', methods=['POST'])
def analyze_sentiment():
    try:
        # Request processing
        text = request.get_json().get('text', '').strip()
        
        # Validation
        if not text:
            return jsonify({
                'success': False,
                'error': 'Invalid input',
                'message': 'Text input is required'
            }), 400
        
        # Analysis
        result = analyzer.analyze_sentiment(text)
        return jsonify({'success': True, 'data': result})
        
    except ValueError as e:
        return jsonify({
            'success': False,
            'error': 'Invalid input',
            'message': str(e)
        }), 400
    except ApiException as e:
        return jsonify({
            'success': False,
            'error': 'Watson API error',
            'message': 'Service temporarily unavailable'
        }), 503
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'message': 'An unexpected error occurred'
        }), 500
```

### 3. Batch Processing Error Handling

```python
def analyze_sentiment_batch(self, texts: list) -> Dict[str, Any]:
    results = []
    errors = []
    
    for i, text in enumerate(texts):
        try:
            result = self.analyze_sentiment(text)
            results.append({
                'index': i,
                'text': text[:100] + '...' if len(text) > 100 else text,
                'result': result
            })
        except Exception as e:
            errors.append({
                'index': i,
                'text': text[:100] + '...' if len(text) > 100 else text,
                'error': str(e)
            })
    
    return {
        'successful_analyses': len(results),
        'failed_analyses': len(errors),
        'results': results,
        'errors': errors
    }
```

## Frontend Error Handling

### JavaScript Error Handling

```javascript
$.ajax({
    url: '/analyze',
    method: 'POST',
    data: JSON.stringify({ text: text }),
    contentType: 'application/json',
    success: function(response) {
        if (response.success) {
            displayResults(response);
        } else {
            displayError(response.message);
        }
    },
    error: function(xhr) {
        const errorResponse = xhr.responseJSON || {};
        const message = errorResponse.message || 'An error occurred during analysis.';
        displayError(message);
    }
});
```

### User-Friendly Error Messages

- **Network Issues**: "Unable to connect to the service. Please check your internet connection."
- **Service Unavailable**: "The sentiment analysis service is temporarily unavailable. Please try again later."
- **Invalid Input**: "Please provide valid text for analysis."
- **Rate Limiting**: "Too many requests. Please wait a moment before trying again."

## Logging and Monitoring

### Logging Configuration

```python
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)
```

### Error Logging Examples

```python
# Input validation errors
logger.warning(f"Invalid input received: {error_details}")

# API errors
logger.error(f"Watson API error: {api_error.message} (Code: {api_error.code})")

# Unexpected errors
logger.critical(f"Unexpected error in sentiment analysis: {str(e)}", exc_info=True)
```

## Health Check and Monitoring

### Health Check Endpoint

```python
@main.route('/health')
def health_check():
    try:
        analyzer = get_sentiment_analyzer()
        service_status = analyzer.get_service_status()
        
        return jsonify({
            'status': 'healthy',
            'watson_service': service_status
        })
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'error': str(e)
        }), 503
```

## Testing Error Scenarios

The test suite includes comprehensive error scenario testing:

- Invalid input handling
- Watson API failures
- Network connectivity issues
- Configuration errors
- Malformed responses

## Best Practices

1. **Always validate input** before processing
2. **Use specific exception types** for different error categories
3. **Log errors appropriately** with sufficient context
4. **Provide user-friendly messages** while logging technical details
5. **Implement circuit breakers** for external service calls
6. **Use health checks** for monitoring service availability
7. **Test error scenarios** thoroughly in the test suite

## Recovery Strategies

1. **Retry Logic**: Implement exponential backoff for transient failures
2. **Fallback Responses**: Provide default responses when services are unavailable
3. **Graceful Degradation**: Continue operating with reduced functionality
4. **User Guidance**: Provide clear instructions for error resolution
