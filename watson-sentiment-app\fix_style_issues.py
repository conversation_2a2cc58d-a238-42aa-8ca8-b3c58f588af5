#!/usr/bin/env python3
"""
<PERSON>ript to fix common style issues in the Watson Sentiment Analysis application
"""

import os
import re
import sys
from pathlib import Path


def remove_trailing_whitespace(file_path):
    """Remove trailing whitespace from a file"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Remove trailing whitespace from each line
    lines = content.splitlines()
    cleaned_lines = [line.rstrip() for line in lines]
    
    # Remove blank lines that contain only whitespace
    final_lines = []
    for line in cleaned_lines:
        if line.strip() == '' and line != '':
            final_lines.append('')  # Convert whitespace-only lines to empty lines
        else:
            final_lines.append(line)
    
    cleaned_content = '\n'.join(final_lines)
    
    # Ensure file ends with a single newline
    if cleaned_content and not cleaned_content.endswith('\n'):
        cleaned_content += '\n'
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(cleaned_content)
    
    return len(lines) - len([l for l in lines if l.strip() == l.rstrip()])


def remove_unused_imports(file_path):
    """Remove unused imports from a file"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Define unused imports to remove
    unused_imports = {
        'app/output_formatter.py': [
            'from typing import Dict, Any, List, Optional',
            'from typing import Dict, Any, Optional'
        ],
        'app/routes.py': [
            'from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for, current_app',
            'from flask import Blueprint, render_template, request, jsonify, current_app'
        ],
        'app/sentiment_analyzer.py': [
            'import json',
            'from typing import Dict, Any, Optional, Union',
            'from typing import Dict, Any'
        ]
    }
    
    # Get relative path
    rel_path = str(file_path).replace('\\', '/').split('watson-sentiment-app/')[-1]
    
    if rel_path in unused_imports:
        for old_import, new_import in zip(unused_imports[rel_path][::2], unused_imports[rel_path][1::2]):
            content = content.replace(old_import, new_import)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)


def fix_long_lines(file_path):
    """Fix lines that are too long by breaking them appropriately"""
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    fixed_lines = []
    for line in lines:
        if len(line.rstrip()) > 100:
            # Simple fix for import lines
            if 'from flask import' in line and len(line.rstrip()) > 100:
                # Break long import lines
                parts = line.strip().split('import ')
                if len(parts) == 2:
                    imports = parts[1].split(', ')
                    if len(imports) > 3:
                        # Break into multiple lines
                        fixed_lines.append(f"{parts[0]}import (\n")
                        for imp in imports:
                            fixed_lines.append(f"    {imp.strip()},\n")
                        fixed_lines.append(")\n")
                        continue
            
            # For other long lines, just keep them as is for now
            fixed_lines.append(line)
        else:
            fixed_lines.append(line)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.writelines(fixed_lines)


def fix_file_style_issues(file_path):
    """Fix style issues in a single file"""
    print(f"Fixing style issues in {file_path}")
    
    # Remove trailing whitespace and fix blank lines
    whitespace_fixes = remove_trailing_whitespace(file_path)
    
    # Remove unused imports (commented out to avoid breaking functionality)
    # remove_unused_imports(file_path)
    
    # Fix long lines (basic fixes only)
    # fix_long_lines(file_path)
    
    return whitespace_fixes


def main():
    """Main function to fix style issues"""
    print("🔧 Fixing Style Issues in Watson Sentiment Analysis Application")
    print("=" * 60)
    
    # Get the project root directory
    project_root = Path(__file__).parent
    app_dir = project_root / 'app'
    
    if not app_dir.exists():
        print("❌ App directory not found!")
        return 1
    
    # Find all Python files in the app directory
    python_files = list(app_dir.glob('*.py'))
    
    total_fixes = 0
    
    for file_path in python_files:
        try:
            fixes = fix_file_style_issues(file_path)
            total_fixes += fixes
            print(f"   ✅ {file_path.name}: {fixes} whitespace issues fixed")
        except Exception as e:
            print(f"   ❌ {file_path.name}: Error - {e}")
    
    print(f"\n📊 Summary:")
    print(f"   Files processed: {len(python_files)}")
    print(f"   Total whitespace fixes: {total_fixes}")
    
    print(f"\n📋 Remaining Issues:")
    print(f"   - Unused imports (manual review recommended)")
    print(f"   - Long lines (context-dependent fixes needed)")
    print(f"   - Indentation issues (manual review recommended)")
    
    print(f"\n✅ Basic style cleanup completed!")
    print(f"💡 Run 'python -m flake8 app/' to check remaining issues")
    
    return 0


if __name__ == '__main__':
    sys.exit(main())
