#!/usr/bin/env python3
"""
Minimal Flask app test
"""
from flask import Flask

app = Flask(__name__)

@app.route('/')
def hello():
    return '''
    <html>
    <head><title>Watson Sentiment Analysis - Test</title></head>
    <body>
        <h1>🎉 Flask Server is Working!</h1>
        <p>The Watson Sentiment Analysis application is running successfully.</p>
        <p><a href="/test">Test endpoint</a></p>
    </body>
    </html>
    '''

@app.route('/test')
def test():
    return {'status': 'success', 'message': 'Flask server is working correctly!'}

if __name__ == '__main__':
    print("Starting minimal Flask test server...")
    print("Visit: http://localhost:5000")
    app.run(host='0.0.0.0', port=5000, debug=True)
