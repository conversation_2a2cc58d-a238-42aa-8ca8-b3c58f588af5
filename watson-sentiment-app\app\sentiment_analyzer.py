"""
Watson NLP Sentiment Analysis Core Module

This module provides the core sentiment analysis functionality using IBM Watson
Natural Language Understanding API.
"""

import json
import logging
from typing import Dict, Any, Optional, Union
from ibm_watson import NaturalLanguageUnderstandingV1
from ibm_watson.natural_language_understanding_v1 import Features, SentimentOptions
from ibm_cloud_sdk_core.authenticators import IAMAuthenticator
from ibm_cloud_sdk_core.api_exception import ApiException


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SentimentAnalyzer:
    """
    Watson NLP Sentiment Analysis class

    Provides methods to analyze sentiment of text using IBM Watson NLP services.
    """

    def __init__(self, api_key: str, service_url: str, version: str = '2022-04-07'):
        """
        Initialize the Watson NLP Sentiment Analyzer

        Args:
            api_key (str): IBM Watson API key
            service_url (str): Watson NLP service URL
            version (str): API version (default: '2022-04-07')
        """
        self.api_key = api_key
        self.service_url = service_url
        self.version = version
        self._nlu_service = None

        # Initialize the service
        self._initialize_service()

    def _initialize_service(self) -> None:
        """
        Initialize the Watson Natural Language Understanding service

        Raises:
            ValueError: If API key or service URL is missing
            Exception: If service initialization fails
        """
        try:
            if not self.api_key:
                raise ValueError("Watson API key is required")

            if not self.service_url:
                raise ValueError("Watson service URL is required")

            # Create authenticator
            authenticator = IAMAuthenticator(self.api_key)

            # Initialize NLU service
            self._nlu_service = NaturalLanguageUnderstandingV1(
                version=self.version,
                authenticator=authenticator
            )

            # Set service URL
            self._nlu_service.set_service_url(self.service_url)

            logger.info("Watson NLU service initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize Watson NLU service: {str(e)}")
            raise

    def analyze_sentiment(self, text: str, **kwargs) -> Dict[str, Any]:
        """
        Analyze sentiment of the provided text

        Args:
            text (str): Text to analyze for sentiment
            **kwargs: Additional parameters for sentiment analysis

        Returns:
            Dict[str, Any]: Sentiment analysis results

        Raises:
            ValueError: If text is empty or None
            ApiException: If Watson API call fails
            Exception: For other unexpected errors
        """
        try:
            # Validate input
            if not text or not text.strip():
                raise ValueError("Text input cannot be empty")

            # Prepare features for analysis
            features = Features(
                sentiment=SentimentOptions()
            )

            # Make API call to Watson NLU
            response = self._nlu_service.analyze(
                text=text.strip(),
                features=features,
                **kwargs
            ).get_result()

            logger.info("Sentiment analysis completed successfully")
            return response

        except ValueError as e:
            logger.error(f"Input validation error: {str(e)}")
            raise

        except ApiException as e:
            logger.error(f"Watson API error: {str(e)}")
            raise

        except Exception as e:
            logger.error(f"Unexpected error during sentiment analysis: {str(e)}")
            raise

    def analyze_sentiment_batch(self, texts: list) -> Dict[str, Any]:
        """
        Analyze sentiment for multiple texts

        Args:
            texts (list): List of texts to analyze

        Returns:
            Dict[str, Any]: Batch sentiment analysis results
        """
        if not texts:
            raise ValueError("Text list cannot be empty")

        results = []
        errors = []

        for i, text in enumerate(texts):
            try:
                result = self.analyze_sentiment(text)
                results.append({
                    'index': i,
                    'text': text[:100] + '...' if len(text) > 100 else text,
                    'result': result
                })
            except Exception as e:
                errors.append({
                    'index': i,
                    'text': text[:100] + '...' if len(text) > 100 else text,
                    'error': str(e)
                })

        return {
            'successful_analyses': len(results),
            'failed_analyses': len(errors),
            'results': results,
            'errors': errors
        }

    def get_service_status(self) -> Dict[str, Any]:
        """
        Check the status of the Watson NLU service

        Returns:
            Dict[str, Any]: Service status information
        """
        try:
            # Try a simple analysis to check service status
            test_response = self.analyze_sentiment("This is a test.")

            return {
                'status': 'healthy',
                'service_url': self.service_url,
                'version': self.version,
                'test_successful': True
            }

        except Exception as e:
            return {
                'status': 'unhealthy',
                'service_url': self.service_url,
                'version': self.version,
                'test_successful': False,
                'error': str(e)
            }


def create_sentiment_analyzer(api_key: str, service_url: str, version: str = '2022-04-07') -> SentimentAnalyzer:
    """
    Factory function to create a SentimentAnalyzer instance

    Args:
        api_key (str): IBM Watson API key
        service_url (str): Watson NLP service URL
        version (str): API version

    Returns:
        SentimentAnalyzer: Configured sentiment analyzer instance
    """
    return SentimentAnalyzer(api_key, service_url, version)
