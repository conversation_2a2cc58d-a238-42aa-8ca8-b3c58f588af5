"""
Flask Routes for Watson Sentiment Analysis Web Application

This module defines the web routes and request handlers for the sentiment analysis application.
"""

import logging
from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for, current_app
from werkzeug.exceptions import BadRequest
from app.sentiment_analyzer import create_sentiment_analyzer
from app.output_formatter import Sentiment<PERSON><PERSON>atter
from ibm_cloud_sdk_core.api_exception import ApiException

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create Blueprint
main = Blueprint('main', __name__)

# Global sentiment analyzer instance
_sentiment_analyzer = None


def get_sentiment_analyzer():
    """
    Get or create sentiment analyzer instance

    Returns:
        SentimentAnalyzer: Configured sentiment analyzer instance
    """
    global _sentiment_analyzer

    if _sentiment_analyzer is None:
        try:
            api_key = current_app.config.get('WATSON_API_KEY')
            service_url = current_app.config.get('WATSON_URL')
            version = current_app.config.get('WATSON_VERSION', '2022-04-07')

            if not api_key or not service_url:
                raise ValueError("Watson API credentials not configured")

            _sentiment_analyzer = create_sentiment_analyzer(api_key, service_url, version)
            logger.info("Sentiment analyzer initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize sentiment analyzer: {str(e)}")
            raise

    return _sentiment_analyzer


@main.route('/')
def index():
    """
    Main application page

    Returns:
        str: Rendered HTML template
    """
    return render_template('index.html')


@main.route('/analyze', methods=['POST'])
def analyze_sentiment():
    """
    Analyze sentiment of submitted text

    Returns:
        str: JSON response with analysis results
    """
    try:
        # Get request data
        if request.is_json:
            data = request.get_json()
            text = data.get('text', '').strip()
        else:
            text = request.form.get('text', '').strip()

        # Validate input
        if not text:
            return jsonify({
                'success': False,
                'error': 'Text input is required',
                'message': 'Please provide text to analyze'
            }), 400

        if len(text) > 10000:  # Reasonable limit
            return jsonify({
                'success': False,
                'error': 'Text too long',
                'message': 'Text must be less than 10,000 characters'
            }), 400

        # Get sentiment analyzer
        analyzer = get_sentiment_analyzer()

        # Perform sentiment analysis
        raw_result = analyzer.analyze_sentiment(text)

        # Format the result
        formatted_result = SentimentFormatter.format_sentiment_result(raw_result, text)
        web_result = SentimentFormatter.format_for_web(formatted_result)

        logger.info(f"Sentiment analysis completed for text length: {len(text)}")

        return jsonify(web_result)

    except ValueError as e:
        logger.error(f"Input validation error: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Invalid input',
            'message': str(e)
        }), 400

    except ApiException as e:
        logger.error(f"Watson API error: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Watson API error',
            'message': 'Failed to analyze sentiment. Please try again later.'
        }), 503

    except Exception as e:
        logger.error(f"Unexpected error during sentiment analysis: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'message': 'An unexpected error occurred. Please try again later.'
        }), 500


@main.route('/analyze-batch', methods=['POST'])
def analyze_batch_sentiment():
    """
    Analyze sentiment for multiple texts

    Returns:
        str: JSON response with batch analysis results
    """
    try:
        # Get request data
        if request.is_json:
            data = request.get_json()
            texts = data.get('texts', [])
        else:
            return jsonify({
                'success': False,
                'error': 'Invalid request format',
                'message': 'Batch analysis requires JSON input'
            }), 400

        # Validate input
        if not texts or not isinstance(texts, list):
            return jsonify({
                'success': False,
                'error': 'Invalid input',
                'message': 'Please provide a list of texts to analyze'
            }), 400

        if len(texts) > 50:  # Reasonable batch limit
            return jsonify({
                'success': False,
                'error': 'Batch too large',
                'message': 'Maximum 50 texts allowed per batch'
            }), 400

        # Filter out empty texts
        valid_texts = [text.strip() for text in texts if text and text.strip()]

        if not valid_texts:
            return jsonify({
                'success': False,
                'error': 'No valid texts',
                'message': 'No valid texts found in the batch'
            }), 400

        # Get sentiment analyzer
        analyzer = get_sentiment_analyzer()

        # Perform batch sentiment analysis
        batch_results = analyzer.analyze_sentiment_batch(valid_texts)

        # Format the results
        formatted_batch = SentimentFormatter.format_batch_results(batch_results)

        logger.info(f"Batch sentiment analysis completed for {len(valid_texts)} texts")

        return jsonify({
            'success': True,
            'data': formatted_batch
        })

    except Exception as e:
        logger.error(f"Error during batch sentiment analysis: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'message': 'An unexpected error occurred during batch analysis.'
        }), 500


@main.route('/health')
def health_check():
    """
    Application health check endpoint

    Returns:
        str: JSON response with health status
    """
    try:
        # Check sentiment analyzer status
        analyzer = get_sentiment_analyzer()
        service_status = analyzer.get_service_status()

        return jsonify({
            'status': 'healthy',
            'timestamp': service_status.get('analyzed_at'),
            'watson_service': service_status
        })

    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return jsonify({
            'status': 'unhealthy',
            'error': str(e)
        }), 503


@main.route('/about')
def about():
    """
    About page

    Returns:
        str: Rendered HTML template
    """
    return render_template('about.html')


@main.errorhandler(404)
def not_found_error(error):
    """Handle 404 errors"""
    return render_template('error.html',
                         error_code=404,
                         error_message="Page not found"), 404


@main.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    return render_template('error.html',
                         error_code=500,
                         error_message="Internal server error"), 500


@main.errorhandler(BadRequest)
def bad_request_error(error):
    """Handle 400 errors"""
    return jsonify({
        'success': False,
        'error': 'Bad request',
        'message': str(error)
    }), 400
