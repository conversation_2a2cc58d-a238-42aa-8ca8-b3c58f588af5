/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  [
    "path",
    {
      d: "M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",
      key: "1r0f0z"
    }
  ],
  ["path", { d: "m14.5 7.5-5 5", key: "3lb6iw" }],
  ["path", { d: "m9.5 7.5 5 5", key: "ko136h" }]
];
const MapPinXInside = createLucideIcon("map-pin-x-inside", __iconNode);

export { __iconNode, MapPinXInside as default };//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/bfcd46e04becbf1670511e8c1e9cb0f1c1d62983/node_modules/lucide-react/dist/esm/icons/map-pin-x-inside.js.map
