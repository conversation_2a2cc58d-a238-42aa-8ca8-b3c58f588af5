"""
Watson Sentiment Analysis Application Package
"""
import os
from flask import Flask
from config import config


def create_app(config_name='default'):
    """
    Application factory pattern for creating Flask app instances

    Args:
        config_name (str): Configuration name ('development', 'testing', 'production')

    Returns:
        Flask: Configured Flask application instance
    """
    # Get the project root directory (parent of app directory)
    basedir = os.path.abspath(os.path.dirname(os.path.dirname(__file__)))

    # Create Flask app with correct template and static folders
    app = Flask(__name__,
                template_folder=os.path.join(basedir, 'templates'),
                static_folder=os.path.join(basedir, 'static'))

    # Load configuration
    app.config.from_object(config[config_name])

    # Initialize extensions here if needed
    # db.init_app(app)
    # migrate.init_app(app, db)

    # Register blueprints
    from app.routes import main
    app.register_blueprint(main)

    return app
