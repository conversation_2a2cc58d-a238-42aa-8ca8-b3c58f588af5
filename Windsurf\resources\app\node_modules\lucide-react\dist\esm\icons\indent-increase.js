/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M21 12H11", key: "wd7e0v" }],
  ["path", { d: "M21 18H11", key: "4wu86t" }],
  ["path", { d: "M21 6H11", key: "6dy1d6" }],
  ["path", { d: "m3 8 4 4-4 4", key: "1a3j6y" }]
];
const IndentIncrease = createLucideIcon("indent-increase", __iconNode);

export { __iconNode, IndentIncrease as default };//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/bfcd46e04becbf1670511e8c1e9cb0f1c1d62983/node_modules/lucide-react/dist/esm/icons/indent-increase.js.map
