#!/usr/bin/env python3
"""
Test script to verify the Flask server is running
"""
import requests
import time
import sys

def test_server():
    """Test if the Flask server is responding"""
    try:
        print("Testing Flask server at http://localhost:5000...")
        response = requests.get('http://localhost:5000', timeout=5)
        print(f"✅ Server is running! Status code: {response.status_code}")
        print(f"Response length: {len(response.text)} characters")
        return True
    except requests.exceptions.ConnectionError:
        print("❌ Server is not running or not accessible")
        return False
    except requests.exceptions.Timeout:
        print("❌ Server request timed out")
        return False
    except Exception as e:
        print(f"❌ Error testing server: {e}")
        return False

def test_health_endpoint():
    """Test the health check endpoint"""
    try:
        print("Testing health endpoint...")
        response = requests.get('http://localhost:5000/health', timeout=5)
        print(f"✅ Health endpoint responding! Status: {response.status_code}")
        if response.status_code == 200:
            print(f"Health response: {response.json()}")
        return True
    except Exception as e:
        print(f"❌ Health endpoint error: {e}")
        return False

if __name__ == '__main__':
    print("🧪 Flask Server Test")
    print("=" * 30)
    
    # Wait a moment for server to start
    time.sleep(2)
    
    if test_server():
        test_health_endpoint()
        print("\n🎉 Server is working correctly!")
        print("🌐 Open http://localhost:5000 in your browser")
    else:
        print("\n❌ Server test failed")
        sys.exit(1)
