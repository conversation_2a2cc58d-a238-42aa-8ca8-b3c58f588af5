"""
Unit tests for the output formatter module
"""

import pytest
import json
from datetime import datetime
from app.output_formatter import <PERSON>timent<PERSON><PERSON><PERSON><PERSON>, format_sentiment_result, format_for_console, format_for_web


class TestSentimentFormatter:
    """Test cases for SentimentFormatter class"""
    
    @pytest.fixture
    def sample_watson_response(self):
        """Sample Watson NLU response for testing"""
        return {
            'usage': {'text_units': 1, 'text_characters': 25, 'features': 1},
            'sentiment': {
                'document': {
                    'score': 0.8,
                    'label': 'positive'
                }
            },
            'language': 'en'
        }
    
    @pytest.fixture
    def sample_negative_response(self):
        """Sample negative Watson NLU response"""
        return {
            'usage': {'text_units': 1, 'text_characters': 30, 'features': 1},
            'sentiment': {
                'document': {
                    'score': -0.6,
                    'label': 'negative'
                }
            },
            'language': 'en'
        }
    
    @pytest.fixture
    def sample_neutral_response(self):
        """Sample neutral Watson NLU response"""
        return {
            'usage': {'text_units': 1, 'text_characters': 20, 'features': 1},
            'sentiment': {
                'document': {
                    'score': 0.1,
                    'label': 'neutral'
                }
            },
            'language': 'en'
        }
    
    def test_format_sentiment_result_positive(self, sample_watson_response):
        """Test formatting positive sentiment result"""
        input_text = "I love this product!"
        result = SentimentFormatter.format_sentiment_result(sample_watson_response, input_text)
        
        assert result['sentiment']['classification'] == 'Positive'
        assert result['sentiment']['label'] == 'positive'
        assert result['sentiment']['score'] == 0.8
        assert result['sentiment']['confidence'] == 80.0
        assert result['sentiment']['emoji'] == '😊'
        assert result['sentiment']['color_class'] == 'positive'
        assert result['analysis']['text_length'] == len(input_text)
        assert result['text_preview'] == input_text
        assert 'analyzed_at' in result['analysis']
    
    def test_format_sentiment_result_negative(self, sample_negative_response):
        """Test formatting negative sentiment result"""
        input_text = "This product is terrible!"
        result = SentimentFormatter.format_sentiment_result(sample_negative_response, input_text)
        
        assert result['sentiment']['classification'] == 'Negative'
        assert result['sentiment']['label'] == 'negative'
        assert result['sentiment']['score'] == -0.6
        assert result['sentiment']['confidence'] == 60.0
        assert result['sentiment']['emoji'] == '😞'
        assert result['sentiment']['color_class'] == 'negative'
    
    def test_format_sentiment_result_neutral(self, sample_neutral_response):
        """Test formatting neutral sentiment result"""
        input_text = "The weather is okay today."
        result = SentimentFormatter.format_sentiment_result(sample_neutral_response, input_text)
        
        assert result['sentiment']['classification'] == 'Neutral'
        assert result['sentiment']['label'] == 'neutral'
        assert result['sentiment']['score'] == 0.1
        assert result['sentiment']['confidence'] == 10.0
        assert result['sentiment']['emoji'] == '😐'
        assert result['sentiment']['color_class'] == 'neutral'
    
    def test_format_sentiment_result_long_text(self, sample_watson_response):
        """Test formatting with long input text"""
        long_text = "This is a very long text " * 10  # 250+ characters
        result = SentimentFormatter.format_sentiment_result(sample_watson_response, long_text)
        
        assert len(result['text_preview']) <= 103  # 100 chars + "..."
        assert result['text_preview'].endswith('...')
        assert result['analysis']['text_length'] == len(long_text)
    
    def test_format_sentiment_result_no_input_text(self, sample_watson_response):
        """Test formatting without input text"""
        result = SentimentFormatter.format_sentiment_result(sample_watson_response)
        
        assert 'text_preview' not in result
        assert result['analysis']['text_length'] == 0
    
    def test_format_sentiment_result_malformed_response(self):
        """Test formatting with malformed Watson response"""
        malformed_response = {'invalid': 'response'}
        result = SentimentFormatter.format_sentiment_result(malformed_response)

        # The formatter should handle malformed responses gracefully
        # It will use default values when sentiment data is missing
        assert 'sentiment' in result
        assert result['sentiment']['classification'] == 'Neutral'
        assert result['sentiment']['confidence'] == 0.0
        assert 'raw_data' in result
        assert result['raw_data'] == malformed_response
    
    def test_format_for_console_success(self, sample_watson_response):
        """Test console formatting for successful result"""
        input_text = "I love this product!"
        formatted_result = SentimentFormatter.format_sentiment_result(sample_watson_response, input_text)
        console_output = SentimentFormatter.format_for_console(formatted_result)
        
        assert "SENTIMENT ANALYSIS RESULTS" in console_output
        assert "Positive" in console_output
        assert "80.0%" in console_output
        assert "0.8" in console_output
        assert input_text in console_output
        assert "=" in console_output  # Check for formatting borders
    
    def test_format_for_console_error(self):
        """Test console formatting for error result"""
        error_result = {'error': 'Test error message'}
        console_output = SentimentFormatter.format_for_console(error_result)
        
        assert "❌ Error:" in console_output
        assert "Test error message" in console_output
    
    def test_format_for_web_success(self, sample_watson_response):
        """Test web formatting for successful result"""
        input_text = "I love this product!"
        formatted_result = SentimentFormatter.format_sentiment_result(sample_watson_response, input_text)
        web_result = SentimentFormatter.format_for_web(formatted_result)
        
        assert web_result['success'] is True
        assert 'display_data' in web_result
        assert web_result['display_data']['title'] == 'Sentiment Analysis Results'
        assert web_result['display_data']['sentiment']['classification'] == 'Positive'
        assert web_result['display_data']['sentiment']['confidence'] == 80.0
        assert web_result['display_data']['type'] == 'success'
    
    def test_format_for_web_error(self):
        """Test web formatting for error result"""
        error_result = {'error': 'Test error message'}
        web_result = SentimentFormatter.format_for_web(error_result)
        
        assert web_result['success'] is False
        assert web_result['error'] == 'Test error message'
        assert web_result['display_data']['type'] == 'error'
        assert web_result['display_data']['title'] == 'Analysis Error'
    
    def test_format_batch_results_success(self, sample_watson_response, sample_negative_response):
        """Test formatting batch results"""
        batch_results = {
            'successful_analyses': 2,
            'failed_analyses': 0,
            'results': [
                {'index': 0, 'text': 'Positive text', 'result': sample_watson_response},
                {'index': 1, 'text': 'Negative text', 'result': sample_negative_response}
            ],
            'errors': []
        }
        
        formatted_batch = SentimentFormatter.format_batch_results(batch_results)
        
        assert formatted_batch['summary']['total_analyzed'] == 2
        assert formatted_batch['summary']['total_failed'] == 0
        assert formatted_batch['summary']['positive_count'] == 1
        assert formatted_batch['summary']['negative_count'] == 1
        assert formatted_batch['summary']['neutral_count'] == 0
        assert len(formatted_batch['results']) == 2
        assert len(formatted_batch['errors']) == 0
    
    def test_format_batch_results_with_errors(self):
        """Test formatting batch results with errors"""
        batch_results = {
            'successful_analyses': 1,
            'failed_analyses': 1,
            'results': [],
            'errors': [
                {'index': 0, 'text': 'Error text', 'error': 'API Error'}
            ]
        }
        
        formatted_batch = SentimentFormatter.format_batch_results(batch_results)
        
        assert formatted_batch['summary']['total_analyzed'] == 1
        assert formatted_batch['summary']['total_failed'] == 1
        assert len(formatted_batch['errors']) == 1
    
    def test_format_for_json_pretty(self, sample_watson_response):
        """Test JSON formatting with pretty printing"""
        input_text = "Test text"
        formatted_result = SentimentFormatter.format_sentiment_result(sample_watson_response, input_text)
        json_output = SentimentFormatter.format_for_json(formatted_result, pretty=True)
        
        # Should be valid JSON
        parsed = json.loads(json_output)
        assert parsed['sentiment']['classification'] == 'Positive'
        
        # Should be pretty formatted (contains newlines and indentation)
        assert '\n' in json_output
        assert '  ' in json_output
    
    def test_format_for_json_compact(self, sample_watson_response):
        """Test JSON formatting without pretty printing"""
        input_text = "Test text"
        formatted_result = SentimentFormatter.format_sentiment_result(sample_watson_response, input_text)
        json_output = SentimentFormatter.format_for_json(formatted_result, pretty=False)
        
        # Should be valid JSON
        parsed = json.loads(json_output)
        assert parsed['sentiment']['classification'] == 'Positive'
        
        # Should be compact (no extra whitespace)
        assert '\n' not in json_output
    
    def test_format_for_json_error(self):
        """Test JSON formatting with non-serializable data"""
        # Create a result with non-serializable data
        bad_result = {'datetime': datetime.now()}  # datetime is not JSON serializable
        json_output = SentimentFormatter.format_for_json(bad_result)
        
        parsed = json.loads(json_output)
        assert 'error' in parsed
        assert 'Failed to format JSON' in parsed['error']
    
    def test_get_sentiment_summary_positive(self, sample_watson_response):
        """Test getting sentiment summary for positive result"""
        formatted_result = SentimentFormatter.format_sentiment_result(sample_watson_response)
        summary = SentimentFormatter.get_sentiment_summary(formatted_result)
        
        assert "😊 Positive" in summary
        assert "80.0% confidence" in summary
    
    def test_get_sentiment_summary_error(self):
        """Test getting sentiment summary for error result"""
        error_result = {'error': 'Test error'}
        summary = SentimentFormatter.get_sentiment_summary(error_result)
        
        assert summary == "Analysis failed"


class TestConvenienceFunctions:
    """Test cases for convenience functions"""
    
    @pytest.fixture
    def sample_watson_response(self):
        """Sample Watson NLU response for testing"""
        return {
            'sentiment': {
                'document': {
                    'score': 0.8,
                    'label': 'positive'
                }
            },
            'language': 'en'
        }
    
    def test_format_sentiment_result_function(self, sample_watson_response):
        """Test the convenience function for formatting sentiment results"""
        result = format_sentiment_result(sample_watson_response, "Test text")
        
        assert result['sentiment']['classification'] == 'Positive'
        assert result['sentiment']['confidence'] == 80.0
    
    def test_format_for_console_function(self, sample_watson_response):
        """Test the convenience function for console formatting"""
        formatted_result = format_sentiment_result(sample_watson_response, "Test text")
        console_output = format_for_console(formatted_result)
        
        assert "SENTIMENT ANALYSIS RESULTS" in console_output
        assert "Positive" in console_output
    
    def test_format_for_web_function(self, sample_watson_response):
        """Test the convenience function for web formatting"""
        formatted_result = format_sentiment_result(sample_watson_response, "Test text")
        web_result = format_for_web(formatted_result)
        
        assert web_result['success'] is True
        assert web_result['display_data']['sentiment']['classification'] == 'Positive'
