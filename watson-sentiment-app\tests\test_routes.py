"""
Unit tests for Flask routes
"""

import pytest
import json
from unittest.mock import Mock, patch
from flask import Flask
from app import create_app
from app.routes import get_sentiment_analyzer
from ibm_cloud_sdk_core.api_exception import ApiException


class TestFlaskRoutes:
    """Test cases for Flask routes"""
    
    @pytest.fixture
    def app(self):
        """Create Flask app for testing"""
        app = create_app('testing')
        app.config['TESTING'] = True
        app.config['WATSON_API_KEY'] = 'test-api-key'
        app.config['WATSON_URL'] = 'https://test-url.com'
        return app
    
    @pytest.fixture
    def client(self, app):
        """Create test client"""
        return app.test_client()
    
    @pytest.fixture
    def mock_watson_response(self):
        """Mock Watson NLU response"""
        return {
            'sentiment': {
                'document': {
                    'score': 0.8,
                    'label': 'positive'
                }
            },
            'language': 'en'
        }
    
    @patch('app.routes.render_template')
    def test_index_route(self, mock_render, client):
        """Test the main index route"""
        mock_render.return_value = '<html><body>Sentiment Analysis</body></html>'
        response = client.get('/')
        assert response.status_code == 200
        mock_render.assert_called_once_with('index.html')
    
    @patch('app.routes.render_template')
    def test_about_route(self, mock_render, client):
        """Test the about route"""
        mock_render.return_value = '<html><body>About This Application</body></html>'
        response = client.get('/about')
        assert response.status_code == 200
        mock_render.assert_called_once_with('about.html')
    
    @patch('app.routes.get_sentiment_analyzer')
    def test_analyze_route_success(self, mock_get_analyzer, client, mock_watson_response):
        """Test successful sentiment analysis via POST"""
        # Mock the sentiment analyzer
        mock_analyzer = Mock()
        mock_analyzer.analyze_sentiment.return_value = mock_watson_response
        mock_get_analyzer.return_value = mock_analyzer
        
        response = client.post('/analyze', 
                             data=json.dumps({'text': 'I love this product!'}),
                             content_type='application/json')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'display_data' in data
    
    @patch('app.routes.get_sentiment_analyzer')
    def test_analyze_route_form_data(self, mock_get_analyzer, client, mock_watson_response):
        """Test sentiment analysis with form data"""
        # Mock the sentiment analyzer
        mock_analyzer = Mock()
        mock_analyzer.analyze_sentiment.return_value = mock_watson_response
        mock_get_analyzer.return_value = mock_analyzer
        
        response = client.post('/analyze', data={'text': 'I love this product!'})
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
    
    def test_analyze_route_empty_text(self, client):
        """Test sentiment analysis with empty text"""
        response = client.post('/analyze', 
                             data=json.dumps({'text': ''}),
                             content_type='application/json')
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['success'] is False
        assert 'Text input is required' in data['error']
    
    def test_analyze_route_missing_text(self, client):
        """Test sentiment analysis with missing text field"""
        response = client.post('/analyze', 
                             data=json.dumps({}),
                             content_type='application/json')
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['success'] is False
    
    def test_analyze_route_text_too_long(self, client):
        """Test sentiment analysis with text that's too long"""
        long_text = 'a' * 10001  # Exceeds 10000 character limit
        response = client.post('/analyze', 
                             data=json.dumps({'text': long_text}),
                             content_type='application/json')
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['success'] is False
        assert 'Text too long' in data['error']
    
    @patch('app.routes.get_sentiment_analyzer')
    def test_analyze_route_watson_api_error(self, mock_get_analyzer, client):
        """Test sentiment analysis with Watson API error"""
        # Mock the sentiment analyzer to raise ApiException
        mock_analyzer = Mock()
        mock_analyzer.analyze_sentiment.side_effect = ApiException(
            message="API Error", code=503
        )
        mock_get_analyzer.return_value = mock_analyzer
        
        response = client.post('/analyze', 
                             data=json.dumps({'text': 'Test text'}),
                             content_type='application/json')
        
        assert response.status_code == 503
        data = json.loads(response.data)
        assert data['success'] is False
        assert 'Watson API error' in data['error']
    
    @patch('app.routes.get_sentiment_analyzer')
    def test_analyze_route_unexpected_error(self, mock_get_analyzer, client):
        """Test sentiment analysis with unexpected error"""
        # Mock the sentiment analyzer to raise generic exception
        mock_analyzer = Mock()
        mock_analyzer.analyze_sentiment.side_effect = Exception("Unexpected error")
        mock_get_analyzer.return_value = mock_analyzer
        
        response = client.post('/analyze', 
                             data=json.dumps({'text': 'Test text'}),
                             content_type='application/json')
        
        assert response.status_code == 500
        data = json.loads(response.data)
        assert data['success'] is False
        assert 'Internal server error' in data['error']
    
    @patch('app.routes.get_sentiment_analyzer')
    def test_analyze_batch_route_success(self, mock_get_analyzer, client, mock_watson_response):
        """Test successful batch sentiment analysis"""
        # Mock the sentiment analyzer
        mock_analyzer = Mock()
        mock_batch_result = {
            'successful_analyses': 2,
            'failed_analyses': 0,
            'results': [
                {'index': 0, 'text': 'Text 1', 'result': mock_watson_response},
                {'index': 1, 'text': 'Text 2', 'result': mock_watson_response}
            ],
            'errors': []
        }
        mock_analyzer.analyze_sentiment_batch.return_value = mock_batch_result
        mock_get_analyzer.return_value = mock_analyzer
        
        response = client.post('/analyze-batch', 
                             data=json.dumps({'texts': ['Text 1', 'Text 2']}),
                             content_type='application/json')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'data' in data
    
    def test_analyze_batch_route_invalid_format(self, client):
        """Test batch analysis with invalid request format"""
        response = client.post('/analyze-batch', data={'texts': ['Text 1']})
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['success'] is False
        assert 'Invalid request format' in data['error']
    
    def test_analyze_batch_route_empty_list(self, client):
        """Test batch analysis with empty text list"""
        response = client.post('/analyze-batch', 
                             data=json.dumps({'texts': []}),
                             content_type='application/json')
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['success'] is False
        assert 'Please provide a list of texts' in data['message']
    
    def test_analyze_batch_route_too_many_texts(self, client):
        """Test batch analysis with too many texts"""
        texts = ['Text'] * 51  # Exceeds limit of 50
        response = client.post('/analyze-batch', 
                             data=json.dumps({'texts': texts}),
                             content_type='application/json')
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['success'] is False
        assert 'Maximum 50 texts allowed' in data['message']
    
    def test_analyze_batch_route_no_valid_texts(self, client):
        """Test batch analysis with no valid texts"""
        response = client.post('/analyze-batch', 
                             data=json.dumps({'texts': ['', '   ', None]}),
                             content_type='application/json')
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['success'] is False
        assert 'No valid texts found' in data['message']
    
    @patch('app.routes.get_sentiment_analyzer')
    def test_health_check_route_healthy(self, mock_get_analyzer, client):
        """Test health check when service is healthy"""
        mock_analyzer = Mock()
        mock_analyzer.get_service_status.return_value = {
            'status': 'healthy',
            'test_successful': True
        }
        mock_get_analyzer.return_value = mock_analyzer
        
        response = client.get('/health')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['status'] == 'healthy'
        assert 'watson_service' in data
    
    @patch('app.routes.get_sentiment_analyzer')
    def test_health_check_route_unhealthy(self, mock_get_analyzer, client):
        """Test health check when service is unhealthy"""
        mock_get_analyzer.side_effect = Exception("Service unavailable")
        
        response = client.get('/health')
        
        assert response.status_code == 503
        data = json.loads(response.data)
        assert data['status'] == 'unhealthy'
        assert 'error' in data
    
    def test_404_error_handler(self, client):
        """Test 404 error handler"""
        response = client.get('/nonexistent-route')
        assert response.status_code == 404
        # Flask's default 404 response contains "Not Found"
        assert b'Not Found' in response.data
    
    @patch('app.routes.get_sentiment_analyzer')
    def test_500_error_handler(self, mock_get_analyzer, client):
        """Test 500 error handler"""
        # Force a 500 error by making the analyzer initialization fail
        mock_get_analyzer.side_effect = Exception("Server error")
        
        response = client.post('/analyze', 
                             data=json.dumps({'text': 'Test'}),
                             content_type='application/json')
        
        assert response.status_code == 500


class TestGetSentimentAnalyzer:
    """Test cases for get_sentiment_analyzer function"""
    
    @pytest.fixture
    def app_context(self):
        """Create app context for testing"""
        app = create_app('testing')
        app.config['WATSON_API_KEY'] = 'test-api-key'
        app.config['WATSON_URL'] = 'https://test-url.com'
        with app.app_context():
            yield app
    
    @patch('app.routes.create_sentiment_analyzer')
    def test_get_sentiment_analyzer_success(self, mock_create, app_context):
        """Test successful sentiment analyzer creation"""
        mock_analyzer = Mock()
        mock_create.return_value = mock_analyzer
        
        # Clear any existing analyzer
        import app.routes
        app.routes._sentiment_analyzer = None
        
        analyzer = get_sentiment_analyzer()
        
        assert analyzer == mock_analyzer
        mock_create.assert_called_once_with(
            'test-api-key', 'https://test-url.com', '2022-04-07'
        )
    
    def test_get_sentiment_analyzer_missing_credentials(self, app_context):
        """Test sentiment analyzer creation with missing credentials"""
        app_context.config['WATSON_API_KEY'] = None
        
        # Clear any existing analyzer
        import app.routes
        app.routes._sentiment_analyzer = None
        
        with pytest.raises(ValueError, match="Watson API credentials not configured"):
            get_sentiment_analyzer()
    
    @patch('app.routes.create_sentiment_analyzer')
    def test_get_sentiment_analyzer_singleton(self, mock_create, app_context):
        """Test that sentiment analyzer is created only once (singleton pattern)"""
        mock_analyzer = Mock()
        mock_create.return_value = mock_analyzer
        
        # Clear any existing analyzer
        import app.routes
        app.routes._sentiment_analyzer = None
        
        # Call twice
        analyzer1 = get_sentiment_analyzer()
        analyzer2 = get_sentiment_analyzer()
        
        # Should be the same instance
        assert analyzer1 == analyzer2
        # Should only be created once
        mock_create.assert_called_once()
